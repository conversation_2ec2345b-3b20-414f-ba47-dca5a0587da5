#!/usr/bin/env python3
"""
Script to extract paystub information from ADP-PayStubs.pdf using PyMuPDF4LLM.
Extracts: Period Beginning, Period Ending, Pay Date, Deposited to, Amount into CSV format.
"""

import pymupdf4llm
import re
import csv
from pathlib import Path
from typing import List, Dict, Optional
import sys


def extract_markdown_from_pdf(pdf_path: str) -> str:
    """Extract markdown content from PDF using PyMuPDF4LLM."""
    try:
        print(f"Extracting content from {pdf_path}...")
        md_text = pymupdf4llm.to_markdown(pdf_path)
        return md_text
    except Exception as e:
        print(f"Error extracting PDF: {e}")
        sys.exit(1)


def parse_paystub_data(markdown_text: str) -> List[Dict[str, str]]:
    """Parse paystub data from markdown text to extract key information."""
    paystubs = []

    # Split by "Earnings Statement" which appears to be the separator for each paystub
    sections = re.split(r'\*\*Earnings Statement\*\*', markdown_text)

    for section in sections:
        if not section.strip():
            continue

        paystub_data = {}

        # ADP format has dates on separate lines after the labels
        # Look for the pattern: Period Beginning:\nPeriod Ending:\nPay Date:\n followed by dates
        date_pattern = r'Period Beginning:\s*\n\s*Period Ending:\s*\n\s*Pay Date:\s*\n\s*(\d{1,2}/\d{1,2}/\d{4})\s*\n\s*(\d{1,2}/\d{1,2}/\d{4})\s*\n\s*(\d{1,2}/\d{1,2}/\d{4})'
        date_match = re.search(date_pattern, section, re.MULTILINE)

        if date_match:
            paystub_data['Period Beginning'] = date_match.group(1)
            paystub_data['Period Ending'] = date_match.group(2)
            paystub_data['Pay Date'] = date_match.group(3)

        # Extract Deposited to (look for the pattern in the deposit section)
        deposited_match = re.search(r'\*\*Deposited\*\*\s+\*\*to the account of\*\*\s*\n\s*\*\*([^*]+)\*\*', section, re.IGNORECASE)
        if deposited_match:
            paystub_data['Deposited to'] = deposited_match.group(1).strip()

        # Extract Amount - look for the dollar amount near account info
        # Comprehensive patterns to handle all ADP format variations
        # Fixed regex to properly capture full amounts with commas
        amount_patterns = [
            # Pattern 1: xxxxxx3380 xxxx xxxx $4,227.72 (fully bold)
            r'\*\*xxxxxx\d+\*\*\s+\*\*[x\s]+\*\*\s*\$([\d,]+(?:\s*\.\s*\d+)?)',
            # Pattern 2: xxxxxx3380 **xxxx xxxx** $5,177.89 (mixed bold)
            r'xxxxxx\d+\s+\*\*[x\s]+\*\*\s*\$([\d,]+(?:\s*\.\s*\d+)?)',
            # Pattern 3: xxxxxx3380** xxxx xxxx $823.87 (account bold, transit not)
            r'xxxxxx\d+\*\*\s+[x\s]+\s*\$([\d,]+(?:\s*\.\s*\d+)?)',
            # Pattern 4: xxxxxx3380 xxxx xxxx $931 .91 (no bold, spaces in decimal)
            r'xxxxxx\d+\s+[x\s]+\s*\$([\d,]+(?:\s*\.\s*\d+)?)',
            # Pattern 5: **amount** followed by $amount with spaces like $4,197. 73
            r'\*\*amount\*\*\s*\n\s*\$([\d,]+(?:\s*\.\s*\d+)?)',
            # Pattern 6: amount followed by $amount with spaces like $875. 33
            r'(?<!\*\*)amount(?!\*\*)\s*\n\s*\$([\d,]+(?:\s*\.\s*\d+)?)',
            # Pattern 7: Checking followed by amount (handle spaces)
            r'Checking\s+\d*\s*-?([\d,]+(?:\s*\.\s*\d+)?)',
            # Pattern 8: **xxxx xxxx** $139,341.59 (bold transit/ABA only) - fixed for large amounts
            r'ABA.*?\*\*[x\s]+\*\*\s*\$([\d,]+(?:\.\d+)?)',
            # Pattern 9: transit ABA amount with $ sign
            r'transit.*?ABA.*?amount.*?\n.*?\$([\d,]+(?:\s*\.\s*\d+)?)',
            # Pattern 10: Direct dollar amounts in deposit sections (for missing amounts)
            r'Deposited.*?account.*?\$([\d,]+(?:\s*\.\s*\d+)?)',
        ]

        for i, pattern in enumerate(amount_patterns):
            amount_match = re.search(pattern, section, re.IGNORECASE | re.MULTILINE)
            if amount_match:
                # Clean up the amount (remove any negative signs and extra spaces)
                raw_amount = amount_match.group(1)
                amount = raw_amount.replace('-', '').strip()
                # Remove ALL spaces within the amount (e.g., "931 .91" -> "931.91", "4,197. 73" -> "4,197.73")
                amount = re.sub(r'\s+', '', amount)

                # Debug output for problematic amounts
                if any(date in section for date in ['10/29/2020', '11/12/2020']):
                    print(f"  DEBUG: Pattern {i+1} matched raw='{raw_amount}' cleaned='{amount}'")

                # Validate amount - must be reasonable for a paystub
                # Convert to float for validation
                try:
                    amount_value = float(amount.replace(',', ''))
                    # Accept amounts between $100 and $1,000,000 (reasonable paystub range)
                    if 100 <= amount_value <= 1000000:
                        paystub_data['Amount'] = amount
                        break
                except ValueError:
                    # Skip invalid amounts
                    if any(date in section for date in ['10/29/2020', '11/12/2020']):
                        print(f"  DEBUG: Invalid amount '{amount}' from raw '{raw_amount}'")
                    continue

        # Only add if we found at least the key date information
        if 'Pay Date' in paystub_data:
            paystubs.append(paystub_data)

    return paystubs


def save_to_csv(paystubs: List[Dict[str, str]], output_file: str):
    """Save paystub data to CSV file."""
    if not paystubs:
        print("No paystub data found to save.")
        return

    fieldnames = ['Period Beginning', 'Period Ending', 'Pay Date', 'Deposited to', 'Amount']

    with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()

        for paystub in paystubs:
            # Ensure all fields exist, even if empty
            row = {}
            for field in fieldnames:
                row[field] = paystub.get(field, '')
            writer.writerow(row)

    print(f"Saved {len(paystubs)} paystub records to {output_file}")


def main():
    pdf_file = "ADP-PayStubs.pdf"
    output_file = "paystubs_extracted.csv"
    debug_file = "paystubs_debug.md"
    
    # Check if PDF exists
    if not Path(pdf_file).exists():
        print(f"Error: {pdf_file} not found in current directory.")
        sys.exit(1)
    
    # Extract markdown from PDF
    markdown_content = extract_markdown_from_pdf(pdf_file)
    
    # Save debug markdown file to inspect the structure
    with open(debug_file, 'w', encoding='utf-8') as f:
        f.write(markdown_content)
    print(f"Debug: Saved raw markdown to {debug_file} for inspection")
    
    # Parse paystub data
    paystubs = parse_paystub_data(markdown_content)
    
    if paystubs:
        print(f"Found {len(paystubs)} paystub records:")
        for i, paystub in enumerate(paystubs, 1):
            print(f"\nPaystub {i}:")
            for key, value in paystub.items():
                print(f"  {key}: {value}")
        
        # Save to CSV
        save_to_csv(paystubs, output_file)
    else:
        print("No paystub data could be extracted. Check the debug markdown file for structure.")
        print("You may need to adjust the regex patterns based on the actual PDF format.")


if __name__ == "__main__":
    main()
