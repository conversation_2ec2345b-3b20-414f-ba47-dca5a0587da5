#!/usr/bin/env python3
"""
Script to extract paystub information from ADP-PayStubs.pdf using pdfplumber.
Extracts: Period Beginning, Period Ending, Pay Date, Deposited to, Amount into CSV format.
"""

import pdfplumber
import re
import csv
from pathlib import Path
from typing import List, Dict, Optional
import sys


def extract_text_from_pdf(pdf_path: str) -> List[Dict]:
    """Extract text from PDF using pdfplumber, preserving page structure."""
    try:
        print(f"Extracting content from {pdf_path} using pdfplumber...")
        pages_data = []
        
        with pdfplumber.open(pdf_path) as pdf:
            print(f"Found {len(pdf.pages)} pages")
            
            for page_num, page in enumerate(pdf.pages, 1):
                print(f"Processing page {page_num}...")
                
                # Extract all text with coordinates
                text = page.extract_text()
                
                # Extract tables if any
                tables = page.extract_tables()
                
                # Get text objects with coordinates for precise extraction
                chars = page.chars
                
                page_data = {
                    'page_num': page_num,
                    'text': text,
                    'tables': tables,
                    'chars': chars
                }
                pages_data.append(page_data)
        
        return pages_data
        
    except Exception as e:
        print(f"Error extracting PDF: {e}")
        sys.exit(1)


def find_paystub_sections(pages_data: List[Dict]) -> List[Dict]:
    """Find individual paystub sections across pages."""
    paystub_sections = []
    
    for page_data in pages_data:
        text = page_data['text']
        if not text:
            continue
            
        # Split by "Earnings Statement" which marks each paystub
        sections = re.split(r'Earnings Statement', text)
        
        for i, section in enumerate(sections):
            if not section.strip():
                continue
                
            # Skip the first section if it doesn't contain paystub data
            if i == 0 and 'Period Beginning' not in section:
                continue
                
            paystub_sections.append({
                'page_num': page_data['page_num'],
                'section_num': i,
                'text': section,
                'tables': page_data['tables']
            })
    
    return paystub_sections


def extract_dates_from_section(section_text: str) -> Dict[str, str]:
    """Extract the three key dates from a paystub section."""
    dates = {}

    # Extract each date individually since they're on the same line
    period_begin_match = re.search(r'Period Beginning:\s*(\d{1,2}/\d{1,2}/\d{4})', section_text)
    if period_begin_match:
        dates['Period Beginning'] = period_begin_match.group(1)

    period_end_match = re.search(r'Period Ending:\s*(\d{1,2}/\d{1,2}/\d{4})', section_text)
    if period_end_match:
        dates['Period Ending'] = period_end_match.group(1)

    pay_date_match = re.search(r'Pay Date:\s*(\d{1,2}/\d{1,2}/\d{4})', section_text)
    if pay_date_match:
        dates['Pay Date'] = pay_date_match.group(1)

    return dates


def extract_deposited_to(section_text: str) -> Optional[str]:
    """Extract 'Deposited to' information."""
    # Look for patterns like "Deposited to the account of" followed by name
    patterns = [
        r'Deposited\s+to\s+the\s+account\s+of\s*\n\s*([A-Z\s]+)',
        r'Deposited\s+to\s+the\s+account\s+of\s*\*\*([^*]+)\*\*',
    ]
    
    for pattern in patterns:
        match = re.search(pattern, section_text, re.IGNORECASE | re.MULTILINE)
        if match:
            return match.group(1).strip()
    
    return None


def extract_amount_from_section(section_text: str) -> Optional[str]:
    """Extract the net pay amount from a paystub section."""

    # Check if this is a problematic section for debugging
    is_problematic = any(date in section_text for date in ['10/29/2020', '12/09/2020', '12/10/2021', '11/10/2021'])

    # Multiple patterns to find dollar amounts, handling spaces in amounts
    amount_patterns = [
        # Pattern 1: Account number followed by dollar amount (with possible spaces)
        r'xxxxxx\d+\s+[x\s]+\s*\$([\d,\s]+(?:\.\s*\d+)?)',

        # Pattern 2: Checking line with amount - more specific to capture full amounts
        r'Checking\s+(?:\d+\s+)?-?(\d{1,3}(?:,\d{3})*(?:\s*\.\s*\d+)?)',

        # Pattern 3: Amount after ABA
        r'ABA.*?\$([\d,\s]+(?:\.\s*\d+)?)',

        # Pattern 4: Direct dollar amounts in account lines
        r'account.*?\$([\d,\s]+(?:\.\s*\d+)?)',

        # Pattern 5: More flexible dollar amount patterns for problematic cases
        r'\$(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)',
    ]

    for i, pattern in enumerate(amount_patterns):
        matches = re.findall(pattern, section_text, re.IGNORECASE | re.MULTILINE)
        if matches:
            if is_problematic:
                print(f"  DEBUG: Pattern {i+1} found matches: {matches}")

            # Process and validate amounts
            amounts = []
            for match in matches:
                # Clean up spaces in the amount more thoroughly
                cleaned_amount = re.sub(r'\s+', '', match)
                # Remove leading/trailing commas or periods
                cleaned_amount = re.sub(r'^[,\.]+|[,\.]+$', '', cleaned_amount)

                if is_problematic:
                    print(f"    Processing match: '{match}' -> '{cleaned_amount}'")

                try:
                    # Convert to float for validation
                    amount_value = float(cleaned_amount.replace(',', ''))
                    if 100 <= amount_value <= 1000000:  # Reasonable range
                        amounts.append((amount_value, cleaned_amount))
                        if is_problematic:
                            print(f"    Valid amount: {cleaned_amount} (${amount_value})")
                except ValueError:
                    if is_problematic:
                        print(f"    Invalid amount: {cleaned_amount}")
                    continue

            if amounts:
                # Return the amount string of the largest value
                result = max(amounts, key=lambda x: x[0])[1]
                if is_problematic:
                    print(f"  DEBUG: Selected amount: {result}")
                return result

    if is_problematic:
        print(f"  DEBUG: No amounts found in section")

    return None


def parse_paystub_data(pages_data: List[Dict]) -> List[Dict[str, str]]:
    """Parse paystub data from extracted pages."""
    paystubs = []
    
    # Find paystub sections
    sections = find_paystub_sections(pages_data)
    print(f"Found {len(sections)} paystub sections")
    
    for section in sections:
        paystub_data = {}
        section_text = section['text']
        
        # Extract dates
        dates = extract_dates_from_section(section_text)
        paystub_data.update(dates)
        
        # Extract deposited to
        deposited_to = extract_deposited_to(section_text)
        if deposited_to:
            paystub_data['Deposited to'] = deposited_to
        
        # Extract amount
        amount = extract_amount_from_section(section_text)
        if amount:
            paystub_data['Amount'] = amount
        
        # Only add if we found at least the pay date
        if 'Pay Date' in paystub_data:
            paystubs.append(paystub_data)
    
    return paystubs


def save_to_csv(paystubs: List[Dict[str, str]], output_file: str):
    """Save paystub data to CSV file."""
    if not paystubs:
        print("No paystub data found to save.")
        return
    
    fieldnames = ['Period Beginning', 'Period Ending', 'Pay Date', 'Deposited to', 'Amount']
    
    with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        
        for paystub in paystubs:
            # Ensure all fields exist, even if empty
            row = {}
            for field in fieldnames:
                row[field] = paystub.get(field, '')
            writer.writerow(row)
    
    print(f"Saved {len(paystubs)} paystub records to {output_file}")


def main():
    pdf_file = "ADP-PayStubs.pdf"
    output_file = "paystubs_pdfplumber.csv"
    debug_file = "paystubs_pdfplumber_debug.txt"

    # Check if PDF exists
    if not Path(pdf_file).exists():
        print(f"Error: {pdf_file} not found in current directory.")
        sys.exit(1)

    # Extract pages data from PDF
    pages_data = extract_text_from_pdf(pdf_file)

    # Save debug output to examine structure - focus on problematic dates
    problematic_dates = ['10/29/2020', '12/09/2020', '12/10/2021', '11/10/2021']

    with open(debug_file, 'w', encoding='utf-8') as f:
        for page_data in pages_data:
            page_text = page_data['text']
            # Check if this page contains any problematic dates
            if any(date in page_text for date in problematic_dates):
                f.write(f"\n=== PAGE {page_data['page_num']} (PROBLEMATIC) ===\n")
                f.write(page_text)
                f.write("\n" + "="*50 + "\n")
    print(f"Debug: Saved problematic pages to {debug_file}")

    # Parse paystub data
    paystubs = parse_paystub_data(pages_data)

    if paystubs:
        print(f"\nFound {len(paystubs)} paystub records:")
        for i, paystub in enumerate(paystubs, 1):
            print(f"\nPaystub {i}:")
            for key, value in paystub.items():
                print(f"  {key}: {value}")

        # Save to CSV
        save_to_csv(paystubs, output_file)
    else:
        print("No paystub data could be extracted.")
        print("Check the debug file to see the text structure.")


if __name__ == "__main__":
    main()
