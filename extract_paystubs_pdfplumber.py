#!/usr/bin/env python3
"""
Script to extract paystub information from ADP-PayStubs.pdf using pdfplumber.
Extracts: Earnings Sections and Net Pay with detailed breakdown.
"""

import pdfplumber
import re
import csv
from pathlib import Path
from typing import List, Dict, Optional, Tuple
import sys
import json


def extract_text_from_pdf(pdf_path: str) -> List[Dict]:
    """Extract text from PDF using pdfplumber, preserving page structure."""
    try:
        print(f"Extracting content from {pdf_path} using pdfplumber...")
        pages_data = []
        
        with pdfplumber.open(pdf_path) as pdf:
            print(f"Found {len(pdf.pages)} pages")
            
            for page_num, page in enumerate(pdf.pages, 1):
                print(f"Processing page {page_num}...")
                
                # Extract all text with coordinates
                text = page.extract_text()
                
                # Extract tables if any
                tables = page.extract_tables()
                
                # Get text objects with coordinates for precise extraction
                chars = page.chars
                
                page_data = {
                    'page_num': page_num,
                    'text': text,
                    'tables': tables,
                    'chars': chars
                }
                pages_data.append(page_data)
        
        return pages_data
        
    except Exception as e:
        print(f"Error extracting PDF: {e}")
        sys.exit(1)


def find_paystub_sections(pages_data: List[Dict]) -> List[Dict]:
    """Find individual paystub sections across pages."""
    paystub_sections = []
    
    for page_data in pages_data:
        text = page_data['text']
        if not text:
            continue
            
        # Split by "Earnings Statement" which marks each paystub
        sections = re.split(r'Earnings Statement', text)
        
        for i, section in enumerate(sections):
            if not section.strip():
                continue
                
            # Skip the first section if it doesn't contain paystub data
            if i == 0 and 'Period Beginning' not in section:
                continue
                
            paystub_sections.append({
                'page_num': page_data['page_num'],
                'section_num': i,
                'text': section,
                'tables': page_data['tables']
            })
    
    return paystub_sections


def extract_dates_from_section(section_text: str) -> Dict[str, str]:
    """Extract the three key dates from a paystub section."""
    dates = {}

    # Extract each date individually since they're on the same line
    period_begin_match = re.search(r'Period Beginning:\s*(\d{1,2}/\d{1,2}/\d{4})', section_text)
    if period_begin_match:
        dates['Period Beginning'] = period_begin_match.group(1)

    period_end_match = re.search(r'Period Ending:\s*(\d{1,2}/\d{1,2}/\d{4})', section_text)
    if period_end_match:
        dates['Period Ending'] = period_end_match.group(1)

    pay_date_match = re.search(r'Pay Date:\s*(\d{1,2}/\d{1,2}/\d{4})', section_text)
    if pay_date_match:
        dates['Pay Date'] = pay_date_match.group(1)

    return dates


def extract_earnings_section(section_text: str) -> List[Dict[str, str]]:
    """Extract detailed earnings information from a paystub section."""
    earnings = []

    # Find the earnings section - it starts after "Earnings" and before "Deductions"
    earnings_match = re.search(r'Earnings\s+rate\s+hours\s+this period\s+year to date(.*?)(?:Deductions|$)',
                              section_text, re.DOTALL | re.IGNORECASE)

    if not earnings_match:
        return earnings

    earnings_text = earnings_match.group(1)

    # Common earnings types to look for
    earnings_patterns = [
        r'(Regular)\s+([\d,]+\.?\d*)\s+([\d,]+\.?\d*)\s+([\d,]+\.?\d*)\s+([\d,]+\.?\d*)',
        r'(Comm Pc)\s+([\d,]+\.?\d*)\s+([\d,]+\.?\d*)',
        r'(Pc Draw)\s+([\d,]+\.?\d*)\s+([\d,]+\.?\d*)',
        r'(Phone Stipend)\s+([\d,]+\.?\d*)',
        r'(Def Comp)\s+([\d,]+\.?\d*)\s+([\d,]+\.?\d*)',
        r'(Incentive Award)\s+([\d,]+\.?\d*)',
        r'(Qtr Unitdistrib)\s+([\d,]+\.?\d*)',
        # More flexible pattern for any earnings line
        r'([A-Za-z][A-Za-z\s]+?)\s+([\d,]+\.?\d*)\s+([\d,]+\.?\d*)\s+([\d,]+\.?\d*)\s+([\d,]+\.?\d*)',
        r'([A-Za-z][A-Za-z\s]+?)\s+([\d,]+\.?\d*)\s+([\d,]+\.?\d*)',
        r'([A-Za-z][A-Za-z\s]+?)\s+([\d,]+\.?\d*)',
    ]

    for line in earnings_text.split('\n'):
        line = line.strip()
        if not line or len(line) < 5:
            continue

        for pattern in earnings_patterns:
            match = re.search(pattern, line)
            if match:
                groups = match.groups()
                earning_type = groups[0].strip()

                # Skip if it's not actually an earnings type
                if earning_type.lower() in ['deductions', 'statutory', 'other', 'pre tax', 'after tax']:
                    continue

                earning_entry = {'type': earning_type}

                # Map the captured groups based on how many we have
                if len(groups) >= 5:  # rate, hours, this_period, ytd
                    earning_entry.update({
                        'rate': groups[1],
                        'hours': groups[2],
                        'this_period': groups[3],
                        'year_to_date': groups[4]
                    })
                elif len(groups) >= 3:  # this_period, ytd
                    earning_entry.update({
                        'this_period': groups[1],
                        'year_to_date': groups[2]
                    })
                elif len(groups) >= 2:  # just amount
                    earning_entry.update({
                        'this_period': groups[1]
                    })

                earnings.append(earning_entry)
                break  # Found a match, move to next line

    return earnings


def extract_net_pay(section_text: str) -> Optional[str]:
    """Extract net pay amount from a paystub section."""
    # Net pay is typically the "Checking" line amount
    checking_patterns = [
        r'Checking\s+(?:-?\s*)?(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)',
        r'Checking\s+(?:-?\s*)?([\d,\s]+(?:\.\s*\d+)?)',
        # More flexible patterns
        r'(?:Deposited|Checking).*?\$?([\d,]+\.?\d*)',
    ]

    for pattern in checking_patterns:
        matches = re.findall(pattern, section_text, re.IGNORECASE)
        if matches:
            for match in matches:
                # Clean up the amount
                cleaned_amount = re.sub(r'\s+', '', match)
                cleaned_amount = re.sub(r'^[,\.]+|[,\.]+$', '', cleaned_amount)

                try:
                    amount_value = float(cleaned_amount.replace(',', ''))
                    if 100 <= amount_value <= 1000000:  # Reasonable range
                        return cleaned_amount
                except ValueError:
                    continue

    return None


def extract_deposited_to(section_text: str) -> Optional[str]:
    """Extract 'Deposited to' information."""
    # Look for patterns like "Deposited to the account of" followed by name
    patterns = [
        r'Deposited\s+to\s+the\s+account\s+of\s*\n\s*([A-Z\s]+)',
        r'Deposited\s+to\s+the\s+account\s+of\s*\*\*([^*]+)\*\*',
    ]
    
    for pattern in patterns:
        match = re.search(pattern, section_text, re.IGNORECASE | re.MULTILINE)
        if match:
            return match.group(1).strip()
    
    return None


def extract_amount_from_section(section_text: str) -> Optional[str]:
    """Extract the net pay amount from a paystub section."""

    # Check if this is a problematic section for debugging
    is_problematic = any(date in section_text for date in ['10/29/2020', '12/09/2020', '12/10/2021', '11/10/2021'])

    # Multiple patterns to find dollar amounts, handling spaces in amounts
    amount_patterns = [
        # Pattern 1: Account number followed by dollar amount (with possible spaces)
        r'xxxxxx\d+\s+[x\s]+\s*\$([\d,\s]+(?:\.\s*\d+)?)',

        # Pattern 2: Checking line with amount - more specific to capture full amounts
        r'Checking\s+(?:\d+\s+)?-?(\d{1,3}(?:,\d{3})*(?:\s*\.\s*\d+)?)',

        # Pattern 3: Amount after ABA
        r'ABA.*?\$([\d,\s]+(?:\.\s*\d+)?)',

        # Pattern 4: Direct dollar amounts in account lines
        r'account.*?\$([\d,\s]+(?:\.\s*\d+)?)',

        # Pattern 5: More flexible dollar amount patterns for problematic cases
        r'\$(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)',
    ]

    for i, pattern in enumerate(amount_patterns):
        matches = re.findall(pattern, section_text, re.IGNORECASE | re.MULTILINE)
        if matches:
            if is_problematic:
                print(f"  DEBUG: Pattern {i+1} found matches: {matches}")

            # Process and validate amounts
            amounts = []
            for match in matches:
                # Clean up spaces in the amount more thoroughly
                cleaned_amount = re.sub(r'\s+', '', match)
                # Remove leading/trailing commas or periods
                cleaned_amount = re.sub(r'^[,\.]+|[,\.]+$', '', cleaned_amount)

                if is_problematic:
                    print(f"    Processing match: '{match}' -> '{cleaned_amount}'")

                try:
                    # Convert to float for validation
                    amount_value = float(cleaned_amount.replace(',', ''))
                    if 100 <= amount_value <= 1000000:  # Reasonable range
                        amounts.append((amount_value, cleaned_amount))
                        if is_problematic:
                            print(f"    Valid amount: {cleaned_amount} (${amount_value})")
                except ValueError:
                    if is_problematic:
                        print(f"    Invalid amount: {cleaned_amount}")
                    continue

            if amounts:
                # Return the amount string of the largest value
                result = max(amounts, key=lambda x: x[0])[1]
                if is_problematic:
                    print(f"  DEBUG: Selected amount: {result}")
                return result

    if is_problematic:
        print(f"  DEBUG: No amounts found in section")

    return None


def parse_paystub_data(pages_data: List[Dict]) -> List[Dict]:
    """Parse paystub data from extracted pages with detailed earnings."""
    paystubs = []

    # Find paystub sections
    sections = find_paystub_sections(pages_data)
    print(f"Found {len(sections)} paystub sections")

    for section in sections:
        section_text = section['text']

        # Extract dates
        dates = extract_dates_from_section(section_text)

        # Only process if we have a pay date
        if 'Pay Date' not in dates:
            continue

        # Extract earnings section
        earnings = extract_earnings_section(section_text)

        # Extract net pay
        net_pay = extract_net_pay(section_text)

        # Extract deposited to
        deposited_to = extract_deposited_to(section_text)

        # Create paystub record
        paystub_data = {
            'pay_date': dates.get('Pay Date', ''),
            'period_beginning': dates.get('Period Beginning', ''),
            'period_ending': dates.get('Period Ending', ''),
            'net_pay': net_pay or '',
            'deposited_to': deposited_to or '',
            'earnings': earnings,
            'earnings_json': json.dumps(earnings) if earnings else ''
        }

        paystubs.append(paystub_data)

    return paystubs


def save_to_csv(paystubs: List[Dict], output_file: str):
    """Save paystub data to CSV file with earnings details."""
    if not paystubs:
        print("No paystub data found to save.")
        return

    # Create two CSV files: summary and detailed earnings
    summary_file = output_file
    earnings_file = output_file.replace('.csv', '_earnings.csv')

    # Save summary data
    summary_fieldnames = ['pay_date', 'period_beginning', 'period_ending', 'net_pay', 'deposited_to']

    with open(summary_file, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=summary_fieldnames)
        writer.writeheader()

        for paystub in paystubs:
            row = {}
            for field in summary_fieldnames:
                row[field] = paystub.get(field, '')
            writer.writerow(row)

    print(f"Saved {len(paystubs)} paystub summary records to {summary_file}")

    # Save detailed earnings data
    earnings_fieldnames = ['pay_date', 'earning_type', 'rate', 'hours', 'this_period', 'year_to_date']

    with open(earnings_file, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=earnings_fieldnames)
        writer.writeheader()

        for paystub in paystubs:
            pay_date = paystub.get('pay_date', '')
            earnings = paystub.get('earnings', [])

            for earning in earnings:
                row = {
                    'pay_date': pay_date,
                    'earning_type': earning.get('type', ''),
                    'rate': earning.get('rate', ''),
                    'hours': earning.get('hours', ''),
                    'this_period': earning.get('this_period', ''),
                    'year_to_date': earning.get('year_to_date', '')
                }
                writer.writerow(row)

    print(f"Saved detailed earnings data to {earnings_file}")


def main():
    pdf_file = "ADP-PayStubs.pdf"
    output_file = "paystubs_earnings_netpay.csv"
    debug_file = "paystubs_earnings_debug.txt"

    # Check if PDF exists
    if not Path(pdf_file).exists():
        print(f"Error: {pdf_file} not found in current directory.")
        sys.exit(1)

    # Extract pages data from PDF
    pages_data = extract_text_from_pdf(pdf_file)

    # Save debug output to examine structure
    with open(debug_file, 'w', encoding='utf-8') as f:
        for page_data in pages_data:
            page_text = page_data['text']
            f.write(f"\n=== PAGE {page_data['page_num']} ===\n")
            f.write(page_text)
            f.write("\n" + "="*50 + "\n")
    print(f"Debug: Saved all pages to {debug_file}")

    # Parse paystub data
    paystubs = parse_paystub_data(pages_data)

    if paystubs:
        print(f"\nFound {len(paystubs)} paystub records:")
        for i, paystub in enumerate(paystubs, 1):
            print(f"\nPaystub {i} ({paystub.get('pay_date', 'Unknown Date')}):")
            print(f"  Period: {paystub.get('period_beginning', '')} to {paystub.get('period_ending', '')}")
            print(f"  Net Pay: ${paystub.get('net_pay', 'Not found')}")
            print(f"  Deposited to: {paystub.get('deposited_to', 'Not found')}")

            earnings = paystub.get('earnings', [])
            if earnings:
                print(f"  Earnings ({len(earnings)} items):")
                for earning in earnings:
                    earning_type = earning.get('type', 'Unknown')
                    this_period = earning.get('this_period', '')
                    print(f"    {earning_type}: ${this_period}")
            else:
                print("  No earnings details extracted")

        # Save to CSV
        save_to_csv(paystubs, output_file)
    else:
        print("No paystub data could be extracted.")
        print("Check the debug file to see the text structure.")


if __name__ == "__main__":
    main()
