#!/usr/bin/env python3
"""
Validation script to check extracted amounts and identify missing/incomplete ones.
"""

import csv
import re
from pathlib import Path


def load_csv_data(csv_file):
    """Load paystub data from CSV."""
    paystubs = []
    with open(csv_file, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        for row in reader:
            paystubs.append(row)
    return paystubs


def validate_amounts(paystubs):
    """Validate extracted amounts and identify issues."""
    issues = []
    
    for i, paystub in enumerate(paystubs, 1):
        amount = paystub.get('Amount', '').strip()
        pay_date = paystub.get('Pay Date', '')
        
        if not amount:
            issues.append({
                'row': i + 1,  # +1 for header row
                'pay_date': pay_date,
                'issue': 'Missing amount',
                'amount': amount
            })
        elif amount.endswith(','):
            issues.append({
                'row': i + 1,
                'pay_date': pay_date,
                'issue': 'Incomplete amount (ends with comma)',
                'amount': amount
            })
        elif not '.' in amount and len(amount.replace(',', '')) < 4:
            issues.append({
                'row': i + 1,
                'pay_date': pay_date,
                'issue': 'Suspicious amount (too short, no decimal)',
                'amount': amount
            })
        elif amount.endswith('.'):
            issues.append({
                'row': i + 1,
                'pay_date': pay_date,
                'issue': 'Incomplete amount (ends with period)',
                'amount': amount
            })
    
    return issues


def search_debug_for_amount(debug_file, pay_date):
    """Search debug markdown for amount patterns around a specific pay date."""
    with open(debug_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Find the section containing this pay date
    pay_date_pattern = re.escape(pay_date)
    sections = re.split(r'\*\*Earnings Statement\*\*', content)
    
    for section in sections:
        if pay_date in section:
            # Look for dollar amounts in this section
            dollar_amounts = re.findall(r'\$[\d,]+(?:\s*\.\s*\d+)?', section)
            if dollar_amounts:
                print(f"  Found dollar amounts in section: {dollar_amounts}")
            
            # Look for account number patterns
            account_patterns = re.findall(r'xxxxxx\d+.*?\$[\d,\s\.]+', section)
            if account_patterns:
                print(f"  Found account patterns: {account_patterns}")
            
            return section[:500] + "..." if len(section) > 500 else section
    
    return None


def main():
    csv_file = "paystubs_pdfplumber.csv"
    debug_file = "paystubs_pdfplumber_debug.txt"
    
    if not Path(csv_file).exists():
        print(f"Error: {csv_file} not found.")
        return
    
    if not Path(debug_file).exists():
        print(f"Error: {debug_file} not found.")
        return
    
    # Load and validate data
    paystubs = load_csv_data(csv_file)
    issues = validate_amounts(paystubs)
    
    print(f"Validation Results:")
    print(f"Total paystubs: {len(paystubs)}")
    print(f"Issues found: {len(issues)}")
    print()
    
    # Group issues by type
    issue_types = {}
    for issue in issues:
        issue_type = issue['issue']
        if issue_type not in issue_types:
            issue_types[issue_type] = []
        issue_types[issue_type].append(issue)
    
    # Report issues
    for issue_type, issue_list in issue_types.items():
        print(f"{issue_type}: {len(issue_list)} cases")
        for issue in issue_list[:5]:  # Show first 5 of each type
            print(f"  Row {issue['row']}: {issue['pay_date']} -> '{issue['amount']}'")
            
            # Search debug file for this pay date
            if issue['pay_date']:
                print(f"  Searching debug for {issue['pay_date']}...")
                section = search_debug_for_amount(debug_file, issue['pay_date'])
        
        if len(issue_list) > 5:
            print(f"  ... and {len(issue_list) - 5} more")
        print()


if __name__ == "__main__":
    main()
